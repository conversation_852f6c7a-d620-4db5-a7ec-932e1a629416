"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { 
  Search, 
  Home, 
  User, 
  Briefcase, 
  Mail, 
  FileText,
  ExternalLink,
  Github,
  Linkedin,
  Twitter
} from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface Command {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
  category: string;
  keywords: string[];
}

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {
  const [search, setSearch] = useState("");
  const [selectedIndex, setSelectedIndex] = useState(0);
  const router = useRouter();

  const commands: Command[] = [
    // Navigation
    {
      id: "home",
      title: "Home",
      description: "Go to homepage",
      icon: Home,
      action: () => router.push("/"),
      category: "Navigation",
      keywords: ["home", "main", "landing"],
    },
    {
      id: "about",
      title: "About",
      description: "Learn more about me",
      icon: User,
      action: () => router.push("/about"),
      category: "Navigation",
      keywords: ["about", "bio", "experience", "skills"],
    },
    {
      id: "projects",
      title: "Projects",
      description: "View my work and projects",
      icon: Briefcase,
      action: () => router.push("/projects"),
      category: "Navigation",
      keywords: ["projects", "work", "portfolio", "showcase"],
    },
    {
      id: "blog",
      title: "Blog",
      description: "Read my latest articles",
      icon: FileText,
      action: () => router.push("/blog"),
      category: "Navigation",
      keywords: ["blog", "articles", "writing", "posts"],
    },
    {
      id: "contact",
      title: "Contact",
      description: "Get in touch with me",
      icon: Mail,
      action: () => router.push("/contact"),
      category: "Navigation",
      keywords: ["contact", "email", "message", "hire"],
    },
    // External Links
    {
      id: "github",
      title: "GitHub",
      description: "View my GitHub profile",
      icon: Github,
      action: () => window.open("https://github.com/ashishkamat", "_blank"),
      category: "Social",
      keywords: ["github", "code", "repositories", "open source"],
    },
    {
      id: "linkedin",
      title: "LinkedIn",
      description: "Connect with me on LinkedIn",
      icon: Linkedin,
      action: () => window.open("https://linkedin.com/in/ashishkamat", "_blank"),
      category: "Social",
      keywords: ["linkedin", "professional", "network", "career"],
    },
    {
      id: "twitter",
      title: "Twitter",
      description: "Follow me on Twitter",
      icon: Twitter,
      action: () => window.open("https://twitter.com/ashishkamat", "_blank"),
      category: "Social",
      keywords: ["twitter", "social", "updates", "thoughts"],
    },
    // Quick Actions
    {
      id: "email",
      title: "Send Email",
      description: "Send me an email directly",
      icon: Mail,
      action: () => window.open("mailto:<EMAIL>", "_blank"),
      category: "Quick Actions",
      keywords: ["email", "contact", "message", "hire"],
    },
    {
      id: "resume",
      title: "Download Resume",
      description: "Download my latest resume",
      icon: ExternalLink,
      action: () => window.open("/resume.pdf", "_blank"),
      category: "Quick Actions",
      keywords: ["resume", "cv", "download", "hire"],
    },
  ];

  const filteredCommands = commands.filter((command) => {
    const searchLower = search.toLowerCase();
    return (
      command.title.toLowerCase().includes(searchLower) ||
      command.description.toLowerCase().includes(searchLower) ||
      command.keywords.some((keyword) => keyword.includes(searchLower))
    );
  });

  const groupedCommands = filteredCommands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = [];
    }
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, Command[]>);

  useEffect(() => {
    setSelectedIndex(0);
  }, [search]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      if (e.key === "ArrowDown") {
        e.preventDefault();
        setSelectedIndex((prev) => 
          prev < filteredCommands.length - 1 ? prev + 1 : 0
        );
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        setSelectedIndex((prev) => 
          prev > 0 ? prev - 1 : filteredCommands.length - 1
        );
      } else if (e.key === "Enter") {
        e.preventDefault();
        if (filteredCommands[selectedIndex]) {
          filteredCommands[selectedIndex].action();
          onOpenChange(false);
          setSearch("");
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, selectedIndex, filteredCommands, onOpenChange]);

  const handleCommandSelect = (command: Command) => {
    command.action();
    onOpenChange(false);
    setSearch("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl p-0 overflow-hidden">
        <DialogHeader className="p-4 pb-0">
          <DialogTitle className="sr-only">Command Palette</DialogTitle>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Type a command or search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 border-0 focus-visible:ring-0 text-base"
              autoFocus
            />
          </div>
        </DialogHeader>

        <div className="max-h-96 overflow-y-auto p-4 pt-0">
          {Object.keys(groupedCommands).length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No commands found for "{search}"
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(groupedCommands).map(([category, commands]) => (
                <div key={category}>
                  <div className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2">
                    {category}
                  </div>
                  <div className="space-y-1">
                    {commands.map((command, index) => {
                      const globalIndex = filteredCommands.indexOf(command);
                      const Icon = command.icon;
                      
                      return (
                        <motion.button
                          key={command.id}
                          onClick={() => handleCommandSelect(command)}
                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${
                            globalIndex === selectedIndex
                              ? "bg-accent text-accent-foreground"
                              : "hover:bg-accent/50"
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className={`p-2 rounded-md ${
                            globalIndex === selectedIndex
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          }`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium">{command.title}</div>
                            <div className="text-sm text-muted-foreground truncate">
                              {command.description}
                            </div>
                          </div>
                          {command.category === "Social" && (
                            <ExternalLink className="h-3 w-3 text-muted-foreground" />
                          )}
                        </motion.button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="border-t p-3 text-xs text-muted-foreground flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Badge variant="outline" className="text-xs px-1.5 py-0.5">↑↓</Badge>
              <span>Navigate</span>
            </div>
            <div className="flex items-center space-x-1">
              <Badge variant="outline" className="text-xs px-1.5 py-0.5">↵</Badge>
              <span>Select</span>
            </div>
            <div className="flex items-center space-x-1">
              <Badge variant="outline" className="text-xs px-1.5 py-0.5">Esc</Badge>
              <span>Close</span>
            </div>
          </div>
          <div className="text-muted-foreground/60">
            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
