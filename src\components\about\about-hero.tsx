"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { Download, MapPin, Calendar, Coffee } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export function AboutHero() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="space-y-4">
              <motion.h1
                className="text-4xl sm:text-5xl lg:text-6xl font-bold"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                About <span className="gradient-text-blue">Me</span>
              </motion.h1>
              
              <motion.p
                className="text-xl text-muted-foreground leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                I'm a passionate full-stack developer and UI/UX designer with over 3 years of experience 
                creating innovative digital solutions that make a real impact.
              </motion.p>
            </div>

            <motion.div
              className="prose prose-lg dark:prose-invert max-w-none"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <p>
                My journey in tech started with a curiosity about how things work behind the scenes. 
                What began as tinkering with HTML and CSS has evolved into a deep passion for creating 
                seamless, user-centered digital experiences.
              </p>
              <p>
                I specialize in modern web technologies like React, Next.js, and TypeScript, with a 
                strong focus on performance, accessibility, and user experience. I believe that great 
                software should not only function flawlessly but also delight users at every interaction.
              </p>
              <p>
                When I'm not coding, you'll find me exploring new technologies, contributing to open-source 
                projects, or sharing knowledge with the developer community through blog posts and mentoring.
              </p>
            </motion.div>

            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Button size="lg" className="group">
                <Download className="mr-2 h-4 w-4 group-hover:animate-bounce" />
                Download Resume
              </Button>
              <Button variant="outline" size="lg">
                <Coffee className="mr-2 h-4 w-4" />
                Let's Chat
              </Button>
            </motion.div>
          </motion.div>

          {/* Image and Info Cards */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="relative w-80 h-80 mx-auto lg:w-96 lg:h-96 mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl animate-pulse-glow" />
              <div className="absolute inset-2 bg-background rounded-2xl overflow-hidden">
                <Image
                  src="/ashish-profile.svg"
                  alt="Ashish Kamat"
                  fill
                  className="object-cover"
                  priority
                />
              </div>
            </div>

            {/* Info Cards */}
            <div className="grid grid-cols-2 gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Card className="text-center hover-lift">
                  <CardContent className="p-4">
                    <MapPin className="h-6 w-6 text-primary mx-auto mb-2" />
                    <div className="font-semibold">Location</div>
                    <div className="text-sm text-muted-foreground">Kathmandu, Nepal</div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <Card className="text-center hover-lift">
                  <CardContent className="p-4">
                    <Calendar className="h-6 w-6 text-primary mx-auto mb-2" />
                    <div className="font-semibold">Experience</div>
                    <div className="text-sm text-muted-foreground">3+ Years</div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
